import React from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { Link } from 'expo-router';
import { CircleAlert as AlertCircle } from 'lucide-react-native';
import { useThemeColors } from '@/lib/store/selectors';
import { useSignUpForm } from '@/lib/hooks/useSignUpForm';
import { ROLE_OPTIONS } from '@/types/auth';
import Dropdown from '@/components/onboarding/Dropdown';

export default function SignUpScreen() {
  const colors = useThemeColors();
  const {
    formData,
    errors,
    loading,
    isValid,
    updateField,
    handleSubmit,
    validateField,
  } = useSignUpForm();

  console.log('errors', errors);
  console.log('formData', formData);
  console.log('isValid', isValid);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    scrollView: {
      flex: 1,
    },
    content: {
      paddingHorizontal: 24,
      paddingVertical: 40,
    },
    title: {
      fontSize: 32,
      fontFamily: 'Inter-Bold',
      color: colors.text,
      marginBottom: 8,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: 16,
      fontFamily: 'Inter-Regular',
      color: colors.textSecondary,
      marginBottom: 32,
      textAlign: 'center',
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 12,
      minHeight: 48,
      backgroundColor: colors.surface,
      borderColor: colors.border,
      marginBottom: 20,
    },
    input: {
      flex: 1,
      fontFamily: 'Inter-Regular',
      fontSize: 16,
      paddingVertical: 12,
      color: colors.text,
    },
    inputError: {
      borderColor: colors.error,
      borderWidth: 1,
    },
    label: {
      fontFamily: 'Inter-Medium',
      fontSize: 16,
      marginBottom: 8,
      color: colors.text,
    },
    required: {
      fontFamily: 'Inter-Medium',
      fontSize: 16,
      color: colors.error,
    },
    errorContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: -12,
      marginBottom: 20,
    },
    errorText: {
      fontFamily: 'Inter-Regular',
      fontSize: 14,
      marginLeft: 6,
      flex: 1,
      color: colors.error,
    },
    button: {
      backgroundColor: colors.primary,
      borderRadius: 8,
      padding: 16,
      alignItems: 'center',
      marginTop: 12,
      marginBottom: 24,
    },
    buttonDisabled: {
      opacity: 0.6,
    },
    buttonText: {
      color: 'white',
      fontFamily: 'Inter-SemiBold',
      fontSize: 16,
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 8,
    },
    footerText: {
      color: colors.textSecondary,
      fontFamily: 'Inter-Regular',
      fontSize: 14,
    },
    link: {
      color: colors.primary,
      fontFamily: 'Inter-SemiBold',
      fontSize: 14,
    },
  });

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.scrollView} keyboardShouldPersistTaps="handled">
        <View style={styles.content}>
          <Text style={styles.title}>Create Account</Text>
          <Text style={styles.subtitle}>
            Join us in your liver health journey
          </Text>

          <Text style={styles.label}>
            Full Name
            <Text style={styles.required}> *</Text>
          </Text>
          <View
            style={[
              styles.inputContainer,
              errors.fullName && styles.inputError,
            ]}
          >
            <TextInput
              style={styles.input}
              placeholder="Full Name"
              placeholderTextColor={colors.textTertiary}
              value={formData.fullName}
              onChangeText={text => updateField('fullName', text)}
              onBlur={() => validateField('fullName')}
              autoCapitalize="words"
              autoComplete="name"
              accessibilityLabel="Full name input"
            />
          </View>
          {errors.fullName && (
            <View style={styles.errorContainer}>
              <AlertCircle size={16} color={colors.error} />
              <Text style={styles.errorText}>{errors.fullName}</Text>
            </View>
          )}

          <Text style={styles.label}>
            Email Address
            <Text style={styles.required}> *</Text>
          </Text>
          <View
            style={[styles.inputContainer, errors.email && styles.inputError]}
          >
            <TextInput
              style={styles.input}
              placeholder="Email Address"
              placeholderTextColor={colors.textTertiary}
              value={formData.email}
              onChangeText={text => updateField('email', text)}
              onBlur={() => validateField('email')}
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
              accessibilityLabel="Email address input"
            />
          </View>
          {errors.email && (
            <View style={styles.errorContainer}>
              <AlertCircle size={16} color={colors.error} />
              <Text style={styles.errorText}>{errors.email}</Text>
            </View>
          )}

          <Dropdown
            label="Role"
            value={formData.role}
            options={[...ROLE_OPTIONS]}
            onSelect={value => updateField('role', value)}
            placeholder="Select your role"
            error={errors.role}
            required
            accessibilityLabel="Role selection dropdown"
          />

          <Text style={styles.label}>
            Password
            <Text style={styles.required}> *</Text>
          </Text>
          <View
            style={[
              styles.inputContainer,
              errors.password && styles.inputError,
            ]}
          >
            <TextInput
              style={styles.input}
              placeholder="Password"
              placeholderTextColor={colors.textTertiary}
              value={formData.password}
              onChangeText={text => updateField('password', text)}
              onBlur={() => validateField('password')}
              secureTextEntry
              autoComplete="new-password"
              accessibilityLabel="Password input"
            />
          </View>
          {errors.password && (
            <View style={styles.errorContainer}>
              <AlertCircle size={16} color={colors.error} />
              <Text style={styles.errorText}>{errors.password}</Text>
            </View>
          )}

          <Text style={styles.label}>
            Confirm Password
            <Text style={styles.required}> *</Text>
          </Text>
          <View
            style={[
              styles.inputContainer,
              errors.confirmPassword && styles.inputError,
            ]}
          >
            <TextInput
              style={styles.input}
              placeholder="Confirm Password"
              placeholderTextColor={colors.textTertiary}
              value={formData.confirmPassword}
              onChangeText={text => updateField('confirmPassword', text)}
              onBlur={() => validateField('confirmPassword')}
              secureTextEntry
              autoComplete="new-password"
              accessibilityLabel="Confirm password input"
            />
          </View>
          {errors.confirmPassword && (
            <View style={styles.errorContainer}>
              <AlertCircle size={16} color={colors.error} />
              <Text style={styles.errorText}>{errors.confirmPassword}</Text>
            </View>
          )}

          <TouchableOpacity
            style={[
              styles.button,
              (loading || !isValid) && styles.buttonDisabled,
            ]}
            onPress={handleSubmit}
            disabled={loading || !isValid}
          >
            <Text style={styles.buttonText}>
              {loading ? 'Creating Account...' : 'Create Account'}
            </Text>
          </TouchableOpacity>

          <View style={styles.footer}>
            <Text style={styles.footerText}>Already have an account? </Text>
            <Link href="/(auth)/sign-in" asChild>
              <TouchableOpacity>
                <Text style={styles.link}>Sign In</Text>
              </TouchableOpacity>
            </Link>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
